package com.reown.sample.wallet.ui.routes.dialog_routes.transaction

enum class Chain(val id: String) {
    ETHEREUM("eip155:1"),
    BASE("eip155:8453"),
    ARBITRUM("eip155:42161"),
    OPTIMISM("eip155:10"),
    SOLANA("solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp")
}

interface Token

enum class StableCoin(val decimals: Int): Token {
    USDC(6),
    USDT(6),
    USDS(18),
}

enum class Coin(val decimals: Int): Token {
    ETH(18)
}

object TokenAddresses {
    private val ADDRESSES = mapOf(
        Chain.ETHEREUM to mapOf(
            StableCoin.USDT to "******************************************",
            StableCoin.USDC to "******************************************"
        ),
        Chain.BASE to mapOf(
            StableCoin.USDC to "******************************************",
            StableCoin.USDT to "******************************************",
            StableCoin.USDS to "******************************************"
        ),
        Chain.ARBITRUM to mapOf(
            StableCoin.USDC to "******************************************",
            StableCoin.USDT to "******************************************"
        ),
        Chain.OPTIMISM to mapOf(
            StableCoin.USDC to "******************************************",
            StableCoin.USDT to "******************************************"
        ),
        Chain.SOLANA to mapOf(
            StableCoin.USDC to "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        )
    )

    private fun getTokenAddress(chain: Chain, token: StableCoin): String {
        return ADDRESSES[chain]?.get(token)
            ?: throw IllegalArgumentException("No address found for $token on $chain")
    }

    fun StableCoin.getAddressOn(chain: Chain): String {
        return getTokenAddress(chain, this)
    }
}
