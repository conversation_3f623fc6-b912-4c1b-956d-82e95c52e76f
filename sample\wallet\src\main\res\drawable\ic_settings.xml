<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M12,15C13.657,15 15,13.657 15,12C15,10.343 13.657,9 12,9C10.343,9 9,10.343 9,12C9,13.657 10.343,15 12,15Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#798686"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M18.727,14.727C18.606,15.002 18.57,15.306 18.624,15.601C18.677,15.895 18.818,16.168 19.027,16.382L19.082,16.436C19.251,16.605 19.385,16.806 19.476,17.027C19.568,17.247 19.615,17.484 19.615,17.723C19.615,17.962 19.568,18.198 19.476,18.419C19.385,18.64 19.251,18.84 19.082,19.009C18.913,19.178 18.712,19.312 18.492,19.404C18.271,19.495 18.034,19.542 17.795,19.542C17.556,19.542 17.32,19.495 17.099,19.404C16.878,19.312 16.678,19.178 16.509,19.009L16.455,18.955C16.24,18.745 15.968,18.604 15.673,18.551C15.378,18.497 15.074,18.534 14.8,18.654C14.531,18.77 14.302,18.961 14.14,19.205C13.979,19.449 13.892,19.735 13.891,20.027V20.182C13.891,20.664 13.699,21.126 13.358,21.468C13.017,21.808 12.555,22 12.073,22C11.59,22 11.128,21.808 10.787,21.468C10.446,21.126 10.255,20.664 10.255,20.182V20.1C10.248,19.799 10.15,19.507 9.975,19.263C9.8,19.018 9.555,18.831 9.273,18.727C8.999,18.606 8.694,18.57 8.399,18.624C8.105,18.677 7.832,18.818 7.618,19.027L7.564,19.082C7.395,19.251 7.194,19.385 6.974,19.476C6.753,19.568 6.516,19.615 6.277,19.615C6.038,19.615 5.802,19.568 5.581,19.476C5.36,19.385 5.16,19.251 4.991,19.082C4.822,18.913 4.688,18.712 4.596,18.492C4.505,18.271 4.458,18.034 4.458,17.795C4.458,17.556 4.505,17.32 4.596,17.099C4.688,16.878 4.822,16.678 4.991,16.509L5.045,16.455C5.255,16.24 5.396,15.968 5.449,15.673C5.503,15.378 5.466,15.074 5.345,14.8C5.23,14.531 5.039,14.302 4.795,14.14C4.551,13.979 4.265,13.892 3.973,13.891H3.818C3.336,13.891 2.874,13.699 2.533,13.358C2.192,13.017 2,12.555 2,12.073C2,11.59 2.192,11.128 2.533,10.787C2.874,10.446 3.336,10.255 3.818,10.255H3.9C4.201,10.248 4.493,10.15 4.738,9.975C4.982,9.8 5.169,9.555 5.273,9.273C5.394,8.999 5.43,8.694 5.376,8.399C5.323,8.105 5.182,7.832 4.973,7.618L4.918,7.564C4.749,7.395 4.615,7.194 4.524,6.974C4.432,6.753 4.385,6.516 4.385,6.277C4.385,6.038 4.432,5.802 4.524,5.581C4.615,5.36 4.749,5.16 4.918,4.991C5.087,4.822 5.288,4.688 5.508,4.596C5.729,4.505 5.966,4.458 6.205,4.458C6.443,4.458 6.68,4.505 6.901,4.596C7.122,4.688 7.322,4.822 7.491,4.991L7.545,5.045C7.76,5.255 8.032,5.396 8.327,5.449C8.622,5.503 8.926,5.466 9.2,5.345H9.273C9.542,5.23 9.771,5.039 9.932,4.795C10.094,4.551 10.181,4.265 10.182,3.973V3.818C10.182,3.336 10.373,2.874 10.714,2.533C11.055,2.192 11.518,2 12,2C12.482,2 12.945,2.192 13.286,2.533C13.627,2.874 13.818,3.336 13.818,3.818V3.9C13.819,4.193 13.906,4.478 14.068,4.722C14.229,4.966 14.458,5.157 14.727,5.273C15.002,5.394 15.306,5.43 15.601,5.376C15.895,5.323 16.168,5.182 16.382,4.973L16.436,4.918C16.605,4.749 16.806,4.615 17.027,4.524C17.247,4.432 17.484,4.385 17.723,4.385C17.962,4.385 18.198,4.432 18.419,4.524C18.64,4.615 18.84,4.749 19.009,4.918C19.178,5.087 19.312,5.288 19.404,5.508C19.495,5.729 19.542,5.966 19.542,6.205C19.542,6.443 19.495,6.68 19.404,6.901C19.312,7.122 19.178,7.322 19.009,7.491L18.955,7.545C18.745,7.76 18.604,8.032 18.551,8.327C18.497,8.622 18.534,8.926 18.654,9.2V9.273C18.77,9.542 18.961,9.771 19.205,9.932C19.449,10.094 19.735,10.181 20.027,10.182H20.182C20.664,10.182 21.126,10.373 21.468,10.714C21.808,11.055 22,11.518 22,12C22,12.482 21.808,12.945 21.468,13.286C21.126,13.627 20.664,13.818 20.182,13.818H20.1C19.808,13.819 19.522,13.906 19.278,14.068C19.034,14.229 18.843,14.458 18.727,14.727Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#798686"
      android:strokeLineCap="round"/>
</vector>
