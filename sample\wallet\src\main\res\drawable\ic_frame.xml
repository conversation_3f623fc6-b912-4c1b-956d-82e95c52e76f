<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="33dp"
    android:height="32dp"
    android:viewportWidth="33"
    android:viewportHeight="32">
  <group>
    <clip-path
        android:pathData="M0.5,0h32v32h-32z"/>
    <path
        android:pathData="M14.436,6.579C14.201,6.345 13.911,6.173 13.593,6.081C13.274,5.988 12.938,5.977 12.614,6.049C12.29,6.121 11.989,6.273 11.74,6.492C11.491,6.71 11.3,6.988 11.186,7.3L4.625,25.347C4.517,25.645 4.482,25.964 4.524,26.278C4.565,26.592 4.68,26.891 4.861,27.151C5.042,27.411 5.282,27.624 5.562,27.771C5.842,27.919 6.154,27.997 6.47,28C6.703,27.998 6.934,27.956 7.153,27.875L25.199,21.313C25.511,21.199 25.789,21.009 26.007,20.759C26.226,20.51 26.379,20.209 26.451,19.885C26.523,19.562 26.512,19.225 26.419,18.906C26.327,18.588 26.156,18.298 25.921,18.063L14.436,6.579ZM8.643,20.141L11.043,13.542L18.958,21.458L12.358,23.858L8.643,20.141ZM20.5,9C20.519,8.324 20.683,7.66 20.98,7.052C21.643,5.729 22.893,5 24.5,5C25.338,5 25.875,4.714 26.206,4.099C26.381,3.755 26.481,3.378 26.5,2.993C26.501,2.727 26.607,2.473 26.796,2.286C26.984,2.1 27.239,1.995 27.504,1.996C27.769,1.997 28.023,2.104 28.21,2.292C28.397,2.48 28.501,2.735 28.5,3C28.5,4.608 27.435,7 24.5,7C23.663,7 23.125,7.286 22.794,7.901C22.619,8.245 22.519,8.622 22.5,9.007C22.5,9.139 22.473,9.269 22.423,9.39C22.372,9.511 22.298,9.621 22.205,9.714C22.111,9.806 22.001,9.879 21.879,9.929C21.758,9.979 21.628,10.004 21.496,10.004C21.365,10.003 21.235,9.977 21.114,9.926C20.993,9.875 20.883,9.801 20.79,9.708C20.698,9.615 20.625,9.504 20.575,9.383C20.525,9.261 20.5,9.131 20.5,9ZM17.5,5V2C17.5,1.735 17.605,1.48 17.793,1.293C17.98,1.105 18.235,1 18.5,1C18.765,1 19.02,1.105 19.207,1.293C19.395,1.48 19.5,1.735 19.5,2V5C19.5,5.265 19.395,5.52 19.207,5.707C19.02,5.895 18.765,6 18.5,6C18.235,6 17.98,5.895 17.793,5.707C17.605,5.52 17.5,5.265 17.5,5ZM30.208,15.292C30.3,15.385 30.374,15.496 30.424,15.617C30.475,15.738 30.5,15.868 30.5,16C30.5,16.131 30.474,16.261 30.424,16.382C30.374,16.504 30.3,16.614 30.207,16.707C30.114,16.8 30.004,16.873 29.882,16.924C29.761,16.974 29.631,17 29.5,17C29.368,16.999 29.238,16.973 29.117,16.923C28.996,16.873 28.885,16.799 28.793,16.706L26.793,14.706C26.605,14.519 26.5,14.264 26.5,13.999C26.5,13.733 26.605,13.479 26.793,13.291C26.98,13.104 27.235,12.998 27.5,12.998C27.765,12.998 28.02,13.104 28.208,13.291L30.208,15.292ZM30.816,9.949L27.816,10.949C27.565,11.033 27.29,11.013 27.053,10.894C26.816,10.776 26.635,10.568 26.551,10.316C26.468,10.065 26.487,9.79 26.606,9.553C26.724,9.316 26.932,9.135 27.184,9.051L30.184,8.051C30.435,7.967 30.71,7.987 30.947,8.106C31.185,8.224 31.365,8.432 31.449,8.684C31.533,8.935 31.513,9.21 31.395,9.447C31.276,9.684 31.068,9.865 30.816,9.949Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="10"
            android:startY="10"
            android:endX="22.5"
            android:endY="25.5"
            android:type="linear">
          <item android:offset="0" android:color="#FF8E4DDD"/>
          <item android:offset="1" android:color="#FF3D8DF7"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
